"""Tests for configuration management"""

import pytest
from pathlib import Path
import tempfile
import os

from src.config import Config, load_config, validate_config


def test_default_config():
    """Test default configuration values"""
    config = Config()
    
    assert config.llm.model == "gpt-4"
    assert config.llm.temperature == 0.2
    assert config.llm.max_tokens == 4000
    assert config.llm.retry_attempts == 3
    
    assert config.logging.level == "INFO"
    assert config.processing.max_enrichment_rounds == 3
    assert config.validation.cypher_timeout == 5000


def test_config_validation():
    """Test configuration validation"""
    config = Config()
    
    # Test missing API key
    errors = validate_config(config)
    assert "OPENAI_API_KEY environment variable is required" in errors
    
    # Test invalid API key
    config.openai_api_key = "invalid-key"
    errors = validate_config(config)
    assert any("must be a valid OpenAI API key" in error for error in errors)
    
    # Test valid API key
    config.openai_api_key = "sk-test123"
    errors = validate_config(config)
    # Should have no API key related errors
    api_key_errors = [e for e in errors if "API key" in e]
    assert len(api_key_errors) == 0


def test_load_config_from_file():
    """Test loading configuration from TOML file"""
    
    # Create temporary config file
    config_content = """
[llm]
model = "gpt-3.5-turbo"
temperature = 0.5

[processing]
max_enrichment_rounds = 5
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
        f.write(config_content)
        temp_path = f.name
    
    try:
        config = load_config(temp_path)
        assert config.llm.model == "gpt-3.5-turbo"
        assert config.llm.temperature == 0.5
        assert config.processing.max_enrichment_rounds == 5
    finally:
        os.unlink(temp_path)


def test_environment_variable_override():
    """Test that environment variables override config file values"""
    
    # Set environment variable
    os.environ["OPENAI_API_KEY"] = "sk-env-test-key"
    os.environ["MEMGRAPH_URI"] = "bolt://test:7687"
    
    try:
        config = load_config()
        assert config.openai_api_key == "sk-env-test-key"
        assert config.memgraph_uri == "bolt://test:7687"
    finally:
        # Clean up environment variables
        os.environ.pop("OPENAI_API_KEY", None)
        os.environ.pop("MEMGRAPH_URI", None)
