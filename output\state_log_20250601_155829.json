[{"url": "https://example.com/test-article", "status": "ProcessingStatus.FAILED", "timestamp": "2025-06-01 13:58:29.112167+00:00", "message": "Processing failed", "metadata": {"processing_time": null, "enrichment_rounds": 0, "validation_retries": 0, "errors": ["status_code: 401, model_name: gpt-4, body: {'message': 'Incorrect API key provided: sk-test-***********lder. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}"], "warnings": []}, "session_id": "9f44ae6c-7c8f-4664-b4f5-32f9510fea23"}]