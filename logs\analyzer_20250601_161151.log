2025-06-01T16:11:51.001943+0200 | INFO | Logging configured
2025-06-01T16:11:51.002171+0200 | INFO | Security Content Analyzer starting with 1 URLs
2025-06-01T16:11:51.170653+0200 | ERROR | Failed to connect to Sequential Thinking MCP server: ClientSession.__init__() missing 1 required positional argument: 'write_stream'
2025-06-01T16:11:51.171047+0200 | WARNING | Failed to initialize Sequential Thinking MCP client: ClientSession.__init__() missing 1 required positional argument: 'write_stream'
2025-06-01T16:11:51.171354+0200 | INFO | Will use fallback sequential thinking implementation
2025-06-01T16:11:51.533731+0200 | INFO | Starting analysis of 1 articles
2025-06-01T16:11:51.534017+0200 | INFO | Starting analysis of: https://httpbin.org/html
2025-06-01T16:11:51.534553+0200 | INFO | Fetching article from: https://httpbin.org/html
2025-06-01T16:11:53.303256+0200 | INFO | Successfully extracted 3594 characters from article
2025-06-01T16:11:53.303509+0200 | INFO | 🧠 Sequential thinking (fallback): assessment
2025-06-01T16:11:53.303738+0200 | INFO | Executing Cypher query: MATCH (t:Technique) WHERE t.name CONTAINS 'phishing' RETURN t...
2025-06-01T16:11:53.406263+0200 | INFO | Successfully completed analysis of: https://httpbin.org/html
2025-06-01T16:11:53.406798+0200 | INFO | 💭 Generated 0 thoughts during analysis
