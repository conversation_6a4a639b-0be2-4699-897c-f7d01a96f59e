"""MCP tool wrappers for Security Content Analyzer"""

import asyncio
import json
import re
import subprocess
from typing import Dict, Any, Optional, List
from urllib.parse import urljoin, urlparse
import httpx
from bs4 import BeautifulSoup
from loguru import logger
import anyio
from mcp import ClientSession, StdioServerParameters


class MCPTools:
    """Wrapper for MCP client interactions"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.http_client = httpx.AsyncClient(
            timeout=config.get("article_fetch_timeout", 30),
            follow_redirects=True,
            headers={
                "User-Agent": "SecurityContentAnalyzer/1.0 (Security Research Tool)"
            },
        )

        # MCP clients
        self.sequential_client = None
        self.memgraph_client = None

        # MCP connection management
        self._sequential_context_manager = None
        self._sequential_streams = None

        # Sequential thinking state
        self.current_thought_number = 0
        self.total_thoughts = 5  # Initial estimate
        self.thought_history = []

    async def initialize_mcp_clients(self):
        """Initialize MCP clients"""
        # For now, skip MCP initialization to avoid hanging issues
        # TODO: Fix MCP client initialization
        logger.info("Skipping MCP initialization - using fallback sequential thinking")

        # Uncomment below when MCP issues are resolved:
        # try:
        #     # Initialize Sequential Thinking MCP client
        #     await self._initialize_sequential_thinking_client()
        #     logger.info("Sequential Thinking MCP client initialized")
        # except Exception as e:
        #     logger.warning(f"Failed to initialize Sequential Thinking MCP client: {e}")
        #     logger.info("Will use fallback sequential thinking implementation")

    async def _initialize_sequential_thinking_client(self):
        """Initialize Sequential Thinking MCP client"""
        try:
            # Create server parameters for Sequential Thinking
            server_params = StdioServerParameters(
                command="npx",
                args=["-y", "@modelcontextprotocol/server-sequential-thinking"],
            )

            # Create stdio streams for the MCP server
            from mcp.client.stdio import stdio_client

            # Store the context manager for later cleanup
            self._sequential_context_manager = stdio_client(server_params)

            # Enter the context manager
            self._sequential_streams = (
                await self._sequential_context_manager.__aenter__()
            )

            # Handle different return formats from stdio_client
            if len(self._sequential_streams) == 2:
                read_stream, write_stream = self._sequential_streams
            else:
                read_stream, write_stream, process = self._sequential_streams

            # Create and initialize client session
            self.sequential_client = ClientSession(read_stream, write_stream)
            await self.sequential_client.initialize()

            logger.info("Sequential Thinking MCP server connected successfully")

        except Exception as e:
            logger.error(f"Failed to connect to Sequential Thinking MCP server: {e}")
            self.sequential_client = None
            # Clean up context manager if it was created
            if self._sequential_context_manager and self._sequential_streams:
                try:
                    await self._sequential_context_manager.__aexit__(None, None, None)
                except:
                    pass
                self._sequential_context_manager = None
                self._sequential_streams = None
            raise

    async def read_article(self, url: str) -> str:
        """Fetch and clean article content"""
        try:
            logger.info(f"Fetching article from: {url}")

            response = await self.http_client.get(url)
            response.raise_for_status()

            # Parse HTML content
            soup = BeautifulSoup(response.content, "html.parser")

            # Remove script and style elements
            for script in soup(["script", "style", "nav", "footer", "header", "aside"]):
                script.decompose()

            # Try to find main content areas
            main_content = None

            # Common content selectors
            content_selectors = [
                "article",
                '[role="main"]',
                ".content",
                ".post-content",
                ".entry-content",
                ".article-content",
                "main",
                "#content",
                ".main-content",
            ]

            for selector in content_selectors:
                elements = soup.select(selector)
                if elements:
                    main_content = elements[0]
                    break

            # Fallback to body if no main content found
            if main_content is None:
                main_content = soup.find("body") or soup

            # Extract text
            text = main_content.get_text(separator="\n", strip=True)

            # Clean up text
            text = re.sub(r"\n\s*\n", "\n\n", text)  # Remove excessive newlines
            text = re.sub(r"[ \t]+", " ", text)  # Normalize spaces

            # Limit content length
            max_length = self.config.get("max_content_length", 50000)
            if len(text) > max_length:
                text = text[:max_length] + "\n\n[Content truncated...]"

            logger.info(f"Successfully extracted {len(text)} characters from article")
            return text

        except httpx.HTTPError as e:
            error_msg = f"HTTP error fetching article: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)
        except Exception as e:
            error_msg = f"Error processing article content: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)

    async def think_sequential(
        self,
        thought_type: str,
        prompt: str,
        context: Optional[Dict] = None,
        output_schema: Optional[Dict] = None,
    ) -> Dict[str, Any]:
        """Execute structured thinking step via Sequential Thinking MCP"""

        if self.sequential_client:
            return await self._think_with_mcp(thought_type, prompt, context)
        else:
            return await self._think_fallback(thought_type, prompt, context)

    async def _think_with_mcp(
        self, thought_type: str, prompt: str, context: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """Use Sequential Thinking MCP server for structured thinking"""

        try:
            # Increment thought number
            self.current_thought_number += 1

            # Determine if we need more thoughts
            next_thought_needed = self.current_thought_number < self.total_thoughts

            # Create the thought content
            thought_content = f"[{thought_type.upper()}] {prompt}"
            if context:
                thought_content += f"\n\nContext: {json.dumps(context, indent=2)}"

            # Call the Sequential Thinking MCP tool
            tool_args = {
                "thought": thought_content,
                "nextThoughtNeeded": next_thought_needed,
                "thoughtNumber": self.current_thought_number,
                "totalThoughts": self.total_thoughts,
                "isRevision": False,
                "needsMoreThoughts": False,
            }

            logger.info(
                f"🧠 Sequential Thinking Step {self.current_thought_number}/{self.total_thoughts}: {thought_type}"
            )
            logger.debug(f"Thought content: {thought_content[:200]}...")

            # Call the MCP tool
            result = await self.sequential_client.call_tool(
                "sequentialthinking", tool_args
            )

            # Parse the result
            if result.content and len(result.content) > 0:
                response_text = (
                    result.content[0].text
                    if hasattr(result.content[0], "text")
                    else str(result.content[0])
                )
                response_data = json.loads(response_text)

                # Store thought in history
                thought_record = {
                    "thought_number": self.current_thought_number,
                    "thought_type": thought_type,
                    "content": thought_content,
                    "response": response_data,
                    "timestamp": asyncio.get_event_loop().time(),
                }
                self.thought_history.append(thought_record)

                # Log the structured thought
                logger.info(
                    f"💭 Thought {self.current_thought_number}: {thought_type} completed"
                )

                # Return structured response
                return {
                    "thought_type": thought_type,
                    "thought_number": self.current_thought_number,
                    "total_thoughts": self.total_thoughts,
                    "reasoning": thought_content,
                    "mcp_response": response_data,
                    "next_thought_needed": next_thought_needed,
                    "conclusion": self._extract_conclusion_from_thought(
                        thought_type, thought_content, response_data
                    ),
                }
            else:
                raise Exception("Empty response from Sequential Thinking MCP")

        except Exception as e:
            logger.error(f"Error in Sequential Thinking MCP: {e}")
            # Fallback to local implementation
            return await self._think_fallback(thought_type, prompt, context)

    def _extract_conclusion_from_thought(
        self, thought_type: str, thought_content: str, mcp_response: Dict
    ) -> Dict[str, Any]:
        """Extract actionable conclusions from the thought process"""

        # This is a simplified extraction - in a real implementation,
        # you might use additional LLM calls to parse the reasoning

        if thought_type == "assessment":
            return {
                "has_sufficient_content": True,
                "can_generate_at": "attack" in thought_content.lower()
                or "technique" in thought_content.lower(),
                "can_generate_dom": "detection" in thought_content.lower()
                or "monitor" in thought_content.lower(),
                "can_generate_pom": "prevention" in thought_content.lower()
                or "mitigation" in thought_content.lower(),
                "confidence": 0.8,
                "missing_aspects": [],
                "suggested_searches": [],
            }
        elif thought_type == "enrichment":
            return {
                "search_queries": ["MITRE ATT&CK techniques", "detection methods"],
                "sources_found": [],
            }
        elif thought_type == "generation":
            return {
                "definitions": {
                    "attack_technique": None,
                    "detection_model": None,
                    "prevention_model": None,
                }
            }
        else:
            return {"result": "processed"}

    async def _think_fallback(
        self, thought_type: str, prompt: str, context: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """Fallback sequential thinking implementation when MCP is not available"""

        # Increment thought number for consistency
        self.current_thought_number += 1

        logger.info(
            f"🧠 Sequential Thinking Step {self.current_thought_number}/{self.total_thoughts}: {thought_type}"
        )

        # Create detailed reasoning based on the prompt
        detailed_reasoning = self._create_detailed_reasoning(
            thought_type, prompt, context
        )

        # Log the reasoning process
        logger.info(f"💭 Reasoning: {detailed_reasoning[:200]}...")

        # Store thought in history
        thought_record = {
            "thought_number": self.current_thought_number,
            "thought_type": thought_type,
            "content": prompt,
            "reasoning": detailed_reasoning,
            "timestamp": asyncio.get_event_loop().time(),
        }
        self.thought_history.append(thought_record)

        # Simple fallback responses with detailed reasoning
        if thought_type == "assessment":
            conclusion = {
                "has_sufficient_content": True,
                "can_generate_at": True,
                "can_generate_dom": False,
                "can_generate_pom": True,
                "confidence": 0.8,
                "missing_aspects": [],
                "suggested_searches": [],
            }
            logger.info(
                f"📊 Assessment conclusion: AT={conclusion['can_generate_at']}, DoM={conclusion['can_generate_dom']}, PoM={conclusion['can_generate_pom']}"
            )

            return {
                "thought_type": "assessment",
                "thought_number": self.current_thought_number,
                "reasoning": detailed_reasoning,
                "conclusion": conclusion,
            }
        elif thought_type == "enrichment":
            conclusion = {
                "search_queries": ["MITRE ATT&CK techniques", "detection methods"],
                "sources_found": [],
            }
            logger.info(
                f"🔍 Enrichment: {len(conclusion['search_queries'])} search queries identified"
            )

            return {
                "thought_type": "enrichment",
                "thought_number": self.current_thought_number,
                "reasoning": detailed_reasoning,
                "conclusion": conclusion,
            }
        elif thought_type == "generation":
            conclusion = {
                "definitions": {
                    "attack_technique": None,
                    "detection_model": None,
                    "prevention_model": None,
                }
            }
            logger.info(f"⚙️ Generation: Preparing to create security definitions")

            return {
                "thought_type": "generation",
                "thought_number": self.current_thought_number,
                "reasoning": detailed_reasoning,
                "conclusion": conclusion,
            }
        else:
            logger.info(f"🔄 Processing: {thought_type} step completed")

            return {
                "thought_type": thought_type,
                "thought_number": self.current_thought_number,
                "reasoning": detailed_reasoning,
                "conclusion": {"result": "processed"},
            }

    def _create_detailed_reasoning(
        self, thought_type: str, prompt: str, context: Optional[Dict] = None
    ) -> str:
        """Create detailed reasoning for the thought process"""

        if thought_type == "assessment":
            return f"""
ASSESSMENT REASONING:
1. Analyzing article content for security relevance
2. Checking for technical details about attack methods
3. Looking for detection indicators and monitoring approaches
4. Evaluating prevention and mitigation strategies
5. Determining confidence level based on content depth

Content preview: {prompt[:300]}...

CONCLUSION: The article contains sufficient technical content to generate security definitions.
Attack techniques can be extracted from the described methods.
Prevention models can be derived from the mitigation strategies discussed.
Detection models require more specific indicators - marking as not feasible for this content.
"""
        elif thought_type == "enrichment":
            return f"""
ENRICHMENT REASONING:
1. Identifying gaps in the current article content
2. Determining what additional context would be valuable
3. Formulating search queries for external sources
4. Prioritizing enrichment based on analysis goals

Current content analysis: {prompt[:200]}...

CONCLUSION: Additional context needed for MITRE ATT&CK mapping and detection methods.
Suggested searches will help fill knowledge gaps.
"""
        elif thought_type == "generation":
            return f"""
GENERATION REASONING:
1. Extracting key attack techniques from article content
2. Identifying prevention strategies and implementation steps
3. Mapping to MITRE ATT&CK framework where applicable
4. Structuring definitions according to required schemas

Content analysis: {prompt[:200]}...

CONCLUSION: Ready to generate structured security definitions based on analyzed content.
"""
        else:
            return f"Processing {thought_type}: {prompt[:200]}..."

    async def execute_cypher(self, query: str, timeout: int = 5000) -> Dict[str, Any]:
        """Execute Cypher query via Memgraph MCP"""

        # Placeholder implementation - will be replaced with actual MCP call
        logger.info(f"Executing Cypher query: {query[:100]}...")

        try:
            # Simulate query execution
            await asyncio.sleep(0.1)  # Simulate network delay

            # Basic query validation
            if not query.strip():
                return {
                    "success": False,
                    "error": "Empty query",
                    "data": None,
                    "execution_time_ms": 0,
                }

            # Check for basic Cypher syntax
            if not any(
                keyword in query.upper()
                for keyword in ["MATCH", "CREATE", "MERGE", "RETURN"]
            ):
                return {
                    "success": False,
                    "error": "Invalid Cypher syntax - missing required keywords",
                    "data": None,
                    "execution_time_ms": 0,
                }

            # Simulate successful execution
            return {
                "success": True,
                "error": None,
                "data": [],  # Empty result set for now
                "execution_time_ms": 50,
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "data": None,
                "execution_time_ms": 0,
            }

    async def search_web(
        self, query: str, max_results: int = 5
    ) -> List[Dict[str, Any]]:
        """Search web for additional context (placeholder)"""

        # Placeholder implementation for web search
        logger.info(f"Web search: {query}")

        # Return empty results for now
        return []

    async def cleanup(self):
        """Clean up resources"""
        await self.http_client.aclose()

        # Close MCP clients
        if self.sequential_client:
            try:
                await self.sequential_client.close()
                logger.info("Sequential Thinking MCP client closed")
            except Exception as e:
                logger.warning(f"Error closing Sequential Thinking MCP client: {e}")

        # Close Sequential Thinking MCP context manager
        if self._sequential_context_manager and self._sequential_streams:
            try:
                await self._sequential_context_manager.__aexit__(None, None, None)
                logger.info("Sequential Thinking MCP context closed")
            except Exception as e:
                logger.warning(f"Error closing Sequential Thinking MCP context: {e}")
            finally:
                self._sequential_context_manager = None
                self._sequential_streams = None

        if self.memgraph_client:
            try:
                await self.memgraph_client.close()
                logger.info("Memgraph MCP client closed")
            except Exception as e:
                logger.warning(f"Error closing Memgraph MCP client: {e}")

    def get_thought_history(self) -> List[Dict[str, Any]]:
        """Get the complete thought history for this session"""
        return self.thought_history.copy()

    def reset_thinking_session(self):
        """Reset the thinking session for a new analysis"""
        self.current_thought_number = 0
        self.total_thoughts = 5
        self.thought_history = []
