2025-06-01T16:12:22.170263+0200 | INFO | Logging configured
2025-06-01T16:12:22.170518+0200 | INFO | Security Content Analyzer starting with 1 URLs
2025-06-01T16:12:22.342168+0200 | ERROR | Failed to connect to Sequential Thinking MCP server: object _AsyncGeneratorContextManager can't be used in 'await' expression
2025-06-01T16:12:22.342410+0200 | WARNING | Failed to initialize Sequential Thinking MCP client: object _AsyncGeneratorContextManager can't be used in 'await' expression
2025-06-01T16:12:22.342505+0200 | INFO | Will use fallback sequential thinking implementation
2025-06-01T16:12:22.722100+0200 | INFO | Starting analysis of 1 articles
2025-06-01T16:12:22.722647+0200 | INFO | Starting analysis of: https://httpbin.org/html
2025-06-01T16:12:22.726871+0200 | INFO | Fetching article from: https://httpbin.org/html
2025-06-01T16:12:24.087649+0200 | INFO | Successfully extracted 3594 characters from article
2025-06-01T16:12:24.087898+0200 | INFO | 🧠 Sequential thinking (fallback): assessment
2025-06-01T16:12:24.088146+0200 | INFO | Executing Cypher query: MATCH (t:Technique) WHERE t.name CONTAINS 'phishing' RETURN t...
2025-06-01T16:12:24.199474+0200 | INFO | Successfully completed analysis of: https://httpbin.org/html
2025-06-01T16:12:24.199705+0200 | INFO | 💭 Generated 0 thoughts during analysis
