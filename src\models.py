"""Pydantic models for Security Content Analyzer"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from enum import Enum
from datetime import datetime
import uuid


class ProcessingStatus(Enum):
    """Status of article processing"""

    PENDING = "pending"
    ASSESSING = "assessing"
    ENRICHING = "enriching"
    GENERATING = "generating"
    VALIDATING = "validating"
    COMPLETE = "complete"
    FAILED = "failed"


class ArticleInput(BaseModel):
    """Input article for processing"""

    url: str
    content: Optional[str] = None  # If pre-fetched

    class Config:
        extra = "forbid"


class AssessmentResult(BaseModel):
    """Result of article content assessment"""

    has_sufficient_content: bool
    can_generate_at: bool
    can_generate_dom: bool
    can_generate_pom: bool
    missing_aspects: List[str] = Field(default_factory=list)
    suggested_searches: List[str] = Field(default_factory=list)
    confidence: float = Field(ge=0, le=1)
    reasoning: str = ""

    class Config:
        extra = "forbid"


class CypherQuery(BaseModel):
    """Cypher query with validation status"""

    name: str
    description: str
    query: str
    validation_status: Optional[str] = None
    validation_error: Optional[str] = None
    execution_time_ms: Optional[float] = None

    class Config:
        extra = "forbid"


class AttackTechnique(BaseModel):
    """Attack Technique definition"""

    name: str
    description: str
    mitre_tactics: List[str] = Field(default_factory=list)
    mitre_technique_id: str = ""
    prerequisites: List[str] = Field(default_factory=list)
    target_systems: List[str] = Field(default_factory=list)
    cypher_queries: List[CypherQuery] = Field(default_factory=list)
    confidence_score: float = Field(ge=0, le=1, default=0.8)

    class Config:
        extra = "forbid"


class DetectionModel(BaseModel):
    """Detection Model definition"""

    name: str
    description: str
    detection_logic: str
    data_sources: List[str] = Field(default_factory=list)
    detection_type: str = Field(
        default="real-time"
    )  # "real-time", "periodic", "forensic"
    expected_output: str = ""
    false_positive_rate: Optional[str] = None
    confidence_score: float = Field(ge=0, le=1, default=0.8)

    class Config:
        extra = "forbid"


class PreventionModel(BaseModel):
    """Prevention Model definition"""

    name: str
    description: str
    prevention_steps: List[str] = Field(default_factory=list)
    implementation_complexity: str = Field(default="medium")  # "low", "medium", "high"
    prerequisites: List[str] = Field(default_factory=list)
    target_platforms: List[str] = Field(default_factory=list)
    effectiveness_rating: Optional[str] = None
    confidence_score: float = Field(ge=0, le=1, default=0.8)

    class Config:
        extra = "forbid"


class EnrichmentSource(BaseModel):
    """Source used for content enrichment"""

    url: str
    title: str = ""
    content_snippet: str = ""
    relevance_score: float = Field(ge=0, le=1)

    class Config:
        extra = "forbid"


class ProcessingResult(BaseModel):
    """Complete result of article processing"""

    url: str
    status: ProcessingStatus
    started_at: datetime
    completed_at: Optional[datetime] = None
    reasoning_session_id: str = Field(default_factory=lambda: str(uuid.uuid4()))

    # Assessment results
    assessment: Optional[AssessmentResult] = None

    # Generated definitions
    attack_technique: Optional[AttackTechnique] = None
    detection_model: Optional[DetectionModel] = None
    prevention_model: Optional[PreventionModel] = None

    # Processing metadata
    enrichment_sources: List[EnrichmentSource] = Field(default_factory=list)
    enrichment_rounds: int = 0
    validation_retries: int = 0
    processing_time_seconds: Optional[float] = None

    # Sequential thinking history
    thought_history: List[Dict[str, Any]] = Field(default_factory=list)

    # Error tracking
    errors: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)

    class Config:
        extra = "forbid"


class StateLog(BaseModel):
    """Track article processing state for future DB storage"""

    url: str
    status: ProcessingStatus
    timestamp: datetime
    message: str
    metadata: Dict[str, Any] = Field(default_factory=dict)
    session_id: str

    class Config:
        extra = "forbid"


class ProcessingMetadata(BaseModel):
    """Metadata for batch processing results"""

    started_at: datetime
    completed_at: datetime
    articles_processed: int
    successful_articles: int
    failed_articles: int
    definitions_generated: Dict[str, int] = Field(default_factory=dict)
    total_processing_time: float

    class Config:
        extra = "forbid"


class BatchResult(BaseModel):
    """Result of processing multiple articles"""

    processing_metadata: ProcessingMetadata
    results: List[ProcessingResult]
    state_logs: List[StateLog]

    class Config:
        extra = "forbid"


# Type aliases for convenience
DefinitionType = Union[AttackTechnique, DetectionModel, PreventionModel]
