2025-06-01T16:11:17.746012+0200 | INFO | Logging configured
2025-06-01T16:11:17.746231+0200 | INFO | Security Content Analyzer starting with 1 URLs
2025-06-01T16:11:17.932746+0200 | ERROR | Failed to connect to Sequential Thinking MCP server: ClientSession.__init__() missing 1 required positional argument: 'write_stream'
2025-06-01T16:11:17.933148+0200 | WARNING | Failed to initialize Sequential Thinking MCP client: ClientSession.__init__() missing 1 required positional argument: 'write_stream'
2025-06-01T16:11:17.933352+0200 | INFO | Will use fallback sequential thinking implementation
2025-06-01T16:11:18.324592+0200 | INFO | Starting analysis of 1 articles
2025-06-01T16:11:18.325188+0200 | INFO | Starting analysis of: https://httpbin.org/html
2025-06-01T16:11:18.325538+0200 | INFO | Fetching article from: https://httpbin.org/html
2025-06-01T16:11:19.302483+0200 | INFO | Successfully extracted 3594 characters from article
2025-06-01T16:11:19.302754+0200 | INFO | 🧠 Sequential thinking (fallback): assessment
2025-06-01T16:11:19.302930+0200 | INFO | Executing Cypher query: MATCH (t:Technique) WHERE t.name CONTAINS 'phishing' RETURN t...
2025-06-01T16:11:19.413335+0200 | INFO | Successfully completed analysis of: https://httpbin.org/html
2025-06-01T16:11:19.413590+0200 | INFO | 💭 Generated 0 thoughts during analysis
