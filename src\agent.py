"""Main SecurityAgent class using Pydantic AI"""

import asyncio
import json
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional
from loguru import logger

# Pydantic AI imports
from pydantic_ai import Agent, RunContext
import logfire

# Local imports
from .models import (
    ProcessingResult,
    ProcessingStatus,
    AssessmentResult,
    AttackTechnique,
    DetectionModel,
    PreventionModel,
    CypherQuery,
    EnrichmentSource,
    StateLog,
)
from .tools import MCPTools
from .config import Config


class SecurityAgent:
    """Main security content analysis agent"""

    def __init__(self, config: Config, mcp_tools: MCPTools):
        self.config = config
        self.tools = mcp_tools

        # Initialize Logfire if token is available
        if config.logfire_token:
            try:
                logfire.configure(token=config.logfire_token)
                logger.info("Logfire configured")
            except Exception as e:
                logger.warning(f"Failed to configure Logfire: {e}")

        # Create main agent
        self.agent = Agent(
            model=f"openai:{config.llm.model}",
            system_prompt=self._build_system_prompt(),
            retries=config.llm.retry_attempts,
        )

    def _build_system_prompt(self) -> str:
        """Build the system prompt for the agent"""
        return """You are a security content analyzer that extracts structured 
        attack techniques, detection models, and prevention models from security articles.
        
        Your role is to:
        1. Assess article content for security relevance and completeness
        2. Enrich content with additional context when needed
        3. Generate structured definitions for Attack Techniques (AT), Detection Models (DoM), and Prevention Models (PoM)
        4. Validate any Cypher queries against a graph database
        
        Use sequential thinking for all analysis steps. Be thorough but efficient.
        Focus on extracting actionable security intelligence from the content.
        
        When generating definitions:
        - Attack Techniques should include MITRE ATT&CK mappings when possible
        - Detection Models should specify data sources and detection logic
        - Prevention Models should include implementation steps and complexity
        - All Cypher queries must be syntactically valid
        
        Always provide confidence scores and reasoning for your decisions."""

    async def _assess_content(self, content: str) -> AssessmentResult:
        """Assess article content for sufficiency and potential"""

        try:
            # Use sequential thinking for assessment
            thinking_result = await self.tools.think_sequential(
                thought_type="assessment",
                prompt=f"""Assess this security article content for:
                1. Sufficient technical detail for generating security definitions
                2. Potential to generate Attack Techniques (AT)
                3. Potential to generate Detection Models (DoM) 
                4. Potential to generate Prevention Models (PoM)
                
                Content preview (first 2000 chars):
                {content[:2000]}
                
                Provide assessment with confidence score and reasoning.""",
                context={"content_length": len(content)},
            )

            conclusion = thinking_result.get("conclusion", {})

            return AssessmentResult(
                has_sufficient_content=conclusion.get("has_sufficient_content", False),
                can_generate_at=conclusion.get("can_generate_at", False),
                can_generate_dom=conclusion.get("can_generate_dom", False),
                can_generate_pom=conclusion.get("can_generate_pom", False),
                missing_aspects=conclusion.get("missing_aspects", []),
                suggested_searches=conclusion.get("suggested_searches", []),
                confidence=conclusion.get("confidence", 0.5),
                reasoning=thinking_result.get("reasoning", ""),
            )

        except Exception as e:
            logger.error(f"Error in content assessment: {e}")
            return AssessmentResult(
                has_sufficient_content=False,
                can_generate_at=False,
                can_generate_dom=False,
                can_generate_pom=False,
                confidence=0.0,
                reasoning=f"Assessment failed: {e}",
            )

    async def _enrich_content(
        self, result: ProcessingResult, search_queries: List[str]
    ) -> None:
        """Enrich content with additional context"""

        max_rounds = self.config.processing.max_enrichment_rounds

        for round_num in range(1, max_rounds + 1):
            if round_num > len(search_queries):
                break

            try:
                query = search_queries[round_num - 1]
                logger.info(f"Enrichment round {round_num}: {query}")

                # Search for additional context
                search_results = await self.tools.search_web(query, max_results=3)

                for search_result in search_results:
                    source = EnrichmentSource(
                        url=search_result.get("url", ""),
                        title=search_result.get("title", ""),
                        content_snippet=search_result.get("snippet", ""),
                        relevance_score=search_result.get("relevance", 0.5),
                    )
                    result.enrichment_sources.append(source)

                result.enrichment_rounds = round_num

            except Exception as e:
                logger.warning(f"Enrichment round {round_num} failed: {e}")
                result.warnings.append(f"Enrichment round {round_num} failed: {e}")

    async def _generate_definitions(
        self, result: ProcessingResult, content: str, assessment: AssessmentResult
    ) -> None:
        """Generate security definitions based on content"""

        try:
            # Generate Attack Technique if applicable
            if assessment.can_generate_at:
                result.attack_technique = await self._generate_attack_technique(content)

            # Generate Detection Model if applicable
            if assessment.can_generate_dom:
                result.detection_model = await self._generate_detection_model(content)

            # Generate Prevention Model if applicable
            if assessment.can_generate_pom:
                result.prevention_model = await self._generate_prevention_model(content)

        except Exception as e:
            logger.error(f"Error generating definitions: {e}")
            result.errors.append(f"Definition generation failed: {e}")

    async def _generate_attack_technique(
        self, content: str
    ) -> Optional[AttackTechnique]:
        """Generate Attack Technique definition"""

        # Placeholder implementation
        # In real implementation, this would use the LLM to extract AT details

        return AttackTechnique(
            name="Sample Attack Technique",
            description="Generated from article content",
            mitre_tactics=["Initial Access"],
            mitre_technique_id="T1566",
            prerequisites=["Network access"],
            target_systems=["Windows", "Linux"],
            cypher_queries=[
                CypherQuery(
                    name="Find related techniques",
                    description="Query to find related attack techniques",
                    query="MATCH (t:Technique) WHERE t.name CONTAINS 'phishing' RETURN t",
                )
            ],
        )

    async def _generate_detection_model(self, content: str) -> Optional[DetectionModel]:
        """Generate Detection Model definition"""

        # Placeholder implementation
        return DetectionModel(
            name="Sample Detection Model",
            description="Generated detection logic",
            detection_logic="Monitor for suspicious email attachments",
            data_sources=["Email logs", "Network traffic"],
            detection_type="real-time",
            expected_output="Alert on suspicious attachment execution",
        )

    async def _generate_prevention_model(
        self, content: str
    ) -> Optional[PreventionModel]:
        """Generate Prevention Model definition"""

        # Placeholder implementation
        return PreventionModel(
            name="Sample Prevention Model",
            description="Generated prevention steps",
            prevention_steps=[
                "Implement email filtering",
                "User security training",
                "Endpoint protection",
            ],
            implementation_complexity="medium",
            prerequisites=["Email security solution"],
            target_platforms=["Windows", "Linux", "macOS"],
        )

    async def _validate_cypher_queries(self, result: ProcessingResult) -> None:
        """Validate Cypher queries in generated definitions"""

        queries_to_validate = []

        # Collect all Cypher queries
        if result.attack_technique:
            queries_to_validate.extend(result.attack_technique.cypher_queries)

        # Validate each query
        for query in queries_to_validate:
            try:
                validation_result = await self.tools.execute_cypher(
                    query.query, timeout=self.config.validation.cypher_timeout
                )

                if validation_result["success"]:
                    query.validation_status = "valid"
                    query.execution_time_ms = validation_result.get("execution_time_ms")
                else:
                    query.validation_status = "invalid"
                    query.validation_error = validation_result.get("error")
                    result.warnings.append(f"Invalid Cypher query: {query.name}")

            except Exception as e:
                query.validation_status = "error"
                query.validation_error = str(e)
                result.warnings.append(f"Cypher validation error for {query.name}: {e}")

    async def process_articles(self, urls: List[str]) -> List[ProcessingResult]:
        """Process multiple articles"""

        results = []

        for url in urls:
            try:
                # Directly call the analysis method
                processing_result = await self._analyze_article_content(url)
                results.append(processing_result)

            except Exception as e:
                logger.error(f"Failed to process article {url}: {e}")

                # Create failed result
                failed_result = ProcessingResult(
                    url=url,
                    status=ProcessingStatus.FAILED,
                    started_at=datetime.now(timezone.utc),
                    completed_at=datetime.now(timezone.utc),
                    errors=[str(e)],
                )
                results.append(failed_result)

        return results

    async def _analyze_article_content(self, article_url: str) -> ProcessingResult:
        """Main analysis workflow using Sequential Thinking"""

        result = ProcessingResult(
            url=article_url,
            status=ProcessingStatus.PENDING,
            started_at=datetime.now(timezone.utc),
        )

        try:
            # Step 1: Read article content
            logger.info(f"Starting analysis of: {article_url}")
            result.status = ProcessingStatus.ASSESSING

            article_content = await self.tools.read_article(article_url)

            # Step 2: Assess content sufficiency
            assessment_result = await self._assess_content(article_content)
            result.assessment = assessment_result

            if not assessment_result.has_sufficient_content:
                result.status = ProcessingStatus.FAILED
                result.errors.append("Insufficient content for analysis")
                return result

            # Step 3: Enrich content if needed
            if assessment_result.suggested_searches:
                result.status = ProcessingStatus.ENRICHING
                await self._enrich_content(result, assessment_result.suggested_searches)

            # Step 4: Generate applicable definitions
            result.status = ProcessingStatus.GENERATING
            await self._generate_definitions(result, article_content, assessment_result)

            # Step 5: Validate Cypher queries
            result.status = ProcessingStatus.VALIDATING
            await self._validate_cypher_queries(result)

            # Complete processing
            result.status = ProcessingStatus.COMPLETE
            result.completed_at = datetime.now(timezone.utc)

            if result.started_at and result.completed_at:
                result.processing_time_seconds = (
                    result.completed_at - result.started_at
                ).total_seconds()

            logger.info(f"Successfully completed analysis of: {article_url}")
            return result

        except Exception as e:
            logger.error(f"Error analyzing article {article_url}: {e}")
            result.status = ProcessingStatus.FAILED
            result.errors.append(str(e))
            result.completed_at = datetime.now(timezone.utc)
            return result
