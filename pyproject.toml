[project]
name = "security-content-analyzer"
version = "0.1.0"
description = "Single agent that analyzes security articles to generate Attack Techniques, Detection Models, and Prevention Models"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "pydantic-ai>=0.0.14",
    "pydantic>=2.0.0",
    "logfire>=0.41.0",
    "loguru>=0.7.0",
    "python-dotenv>=1.0.0",
    "toml>=0.10.0",
    "mcp>=1.0.0",
    "httpx>=0.27.0",
    "click>=8.0.0",
    "beautifulsoup4>=4.12.0",
    "lxml>=4.9.0",
    "anyio>=4.0.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[project.scripts]
security-analyzer = "src.main:cli"
