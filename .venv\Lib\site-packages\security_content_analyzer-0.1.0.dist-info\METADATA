Metadata-Version: 2.4
Name: security-content-analyzer
Version: 0.1.0
Summary: Single agent that analyzes security articles to generate Attack Techniques, Detection Models, and Prevention Models
Requires-Python: >=3.13
Requires-Dist: anyio>=4.0.0
Requires-Dist: beautifulsoup4>=4.12.0
Requires-Dist: click>=8.0.0
Requires-Dist: httpx>=0.27.0
Requires-Dist: logfire>=0.41.0
Requires-Dist: loguru>=0.7.0
Requires-Dist: lxml>=4.9.0
Requires-Dist: mcp>=1.0.0
Requires-Dist: pydantic-ai>=0.0.14
Requires-Dist: pydantic>=2.0.0
Requires-Dist: python-dotenv>=1.0.0
Requires-Dist: pyyaml>=6.0.0
Requires-Dist: toml>=0.10.0
Description-Content-Type: text/markdown

# Security Content Analyzer

A single intelligent agent that analyzes security articles to generate Attack Techniques (AT), Detection Models (DoM), and Prevention Models (PoM). The agent uses Sequential Thinking MCP for structured reasoning and validates generated content against Memgraph.

## Features

- **Single Agent Architecture**: One intelligent agent orchestrates all analysis logic
- **Sequential Thinking**: Uses MCP for structured reasoning and decision-making
- **Memgraph Validation**: Validates generated Cypher queries against graph database
- **Comprehensive Output**: Generates structured JSON with AT, DoM, and PoM definitions
- **State Tracking**: Logs all processing states for future database storage
- **Error Resilience**: Graceful handling of failures with retry logic

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd security-content-analyzer
```

2. Install dependencies using uv:
```bash
uv sync
```

3. Copy environment template and configure:
```bash
cp .env.template .env
# Edit .env with your API keys and configuration
```

## Configuration

### Environment Variables
- `OPENAI_API_KEY`: Your OpenAI API key for the LLM
- `LOGFIRE_TOKEN`: (Optional) Logfire token for enhanced logging
- `MEMGRAPH_URI`: Memgraph database connection URI
- `MEMGRAPH_USERNAME`: (Optional) Memgraph username
- `MEMGRAPH_PASSWORD`: (Optional) Memgraph password

### Settings
Edit `config/settings.toml` to customize:
- LLM model and parameters
- Logging configuration
- MCP server paths
- Processing timeouts and limits
- Output formatting

## Usage

### Command Line Interface

Analyze single article:
```bash
uv run security-analyzer https://example.com/security-article
```

Analyze multiple articles:
```bash
uv run security-analyzer https://article1.com https://article2.com https://article3.com
```

### Output

The tool generates two main output files in the `output/` directory:

1. **Definitions File** (`definitions_YYYYMMDD_HHMMSS.json`): Contains generated AT, DoM, and PoM definitions
2. **State Log** (`state_log_YYYYMMDD_HHMMSS.json`): Contains processing state information for each article

## Architecture

### Core Components

- **SecurityAgent** (`src/agent.py`): Main agent class using Pydantic AI
- **MCPTools** (`src/tools.py`): Wrappers for MCP server interactions
- **Models** (`src/models.py`): Pydantic models for all data structures
- **Configuration** (`src/config.py`): Configuration management
- **CLI** (`src/main.py`): Command-line interface and entry point

### Processing Workflow

1. **Assessment**: Analyze article content for sufficiency
2. **Enrichment**: Gather additional context if needed (max 3 rounds)
3. **Generation**: Create AT, DoM, and PoM definitions as applicable
4. **Validation**: Validate Cypher queries against Memgraph
5. **Output**: Generate structured JSON results

## Development

### Project Structure
```
security-content-analyzer/
├── src/
│   ├── __init__.py
│   ├── agent.py          # Main SecurityAgent class
│   ├── tools.py          # MCP tool wrappers
│   ├── models.py         # Pydantic models for I/O
│   ├── config.py         # Configuration management
│   └── main.py           # CLI entry point
├── config/
│   └── settings.toml     # Application settings
├── output/               # Generated JSON outputs
├── logs/                 # Application logs
├── .env                  # Environment variables
├── pyproject.toml        # Project dependencies
└── README.md
```

### Testing

Run tests with:
```bash
uv run pytest
```

### Logging

Logs are written to both console and rotating log files in the `logs/` directory. Configure logging level and format in `config/settings.toml`.

## Requirements

- Python 3.13+
- OpenAI API access
- Sequential Thinking MCP server
- Memgraph MCP server
- Memgraph database instance

## License

[Add your license here]