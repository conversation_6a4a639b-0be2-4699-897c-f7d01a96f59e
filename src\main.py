"""CLI entry point for Security Content Analyzer"""

import asyncio
import json
import sys
from datetime import datetime
from pathlib import Path
from typing import List

import click
from loguru import logger

from .config import Config, load_config, validate_config, ensure_directories
from .tools import MCPTools
from .agent import SecurityAgent
from .models import BatchResult, ProcessingMetadata, StateLog


async def setup_logging(config: Config) -> None:
    """Setup logging configuration"""

    # Remove default logger
    logger.remove()

    # Add console logger
    logger.add(
        sys.stderr,
        level=config.logging.level,
        format=config.logging.format,
        colorize=True,
    )

    # Add file logger
    ensure_directories()
    log_file = (
        Path(__file__).parent.parent
        / "logs"
        / f"analyzer_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    )
    logger.add(
        log_file,
        level=config.logging.level,
        format=config.logging.format,
        rotation=config.logging.rotation,
        retention="30 days",
    )

    logger.info("Logging configured")


async def initialize_tools(config: Config) -> MCPTools:
    """Initialize MCP tools"""

    tools_config = {
        "article_fetch_timeout": config.processing.article_fetch_timeout,
        "max_content_length": config.processing.max_content_length,
        "cypher_timeout": config.validation.cypher_timeout,
    }

    tools = MCPTools(tools_config)
    await tools.initialize_mcp_clients()

    return tools


async def process_articles_async(urls: List[str], config: Config) -> BatchResult:
    """Process articles asynchronously"""

    start_time = datetime.now()
    state_logs = []

    # Initialize tools and agent
    tools = await initialize_tools(config)
    agent = SecurityAgent(config, tools)

    try:
        # Process articles
        logger.info(f"Starting analysis of {len(urls)} articles")

        results = await agent.process_articles(urls)

        # Generate state logs
        for result in results:
            state_log = StateLog(
                url=result.url,
                status=result.status,
                timestamp=result.completed_at or result.started_at,
                message=f"Processing {result.status.value}",
                metadata={
                    "processing_time": result.processing_time_seconds,
                    "enrichment_rounds": result.enrichment_rounds,
                    "validation_retries": result.validation_retries,
                    "errors": result.errors,
                    "warnings": result.warnings,
                },
                session_id=result.reasoning_session_id,
            )
            state_logs.append(state_log)

        # Calculate metadata
        end_time = datetime.now()
        successful_articles = sum(1 for r in results if r.status.value == "complete")
        failed_articles = len(results) - successful_articles

        definitions_generated = {
            "attack_techniques": sum(
                1 for r in results if r.attack_technique is not None
            ),
            "detection_models": sum(
                1 for r in results if r.detection_model is not None
            ),
            "prevention_models": sum(
                1 for r in results if r.prevention_model is not None
            ),
        }

        metadata = ProcessingMetadata(
            started_at=start_time,
            completed_at=end_time,
            articles_processed=len(urls),
            successful_articles=successful_articles,
            failed_articles=failed_articles,
            definitions_generated=definitions_generated,
            total_processing_time=(end_time - start_time).total_seconds(),
        )

        return BatchResult(
            processing_metadata=metadata, results=results, state_logs=state_logs
        )

    finally:
        await tools.cleanup()


def save_results(batch_result: BatchResult, config: Config) -> tuple[Path, Path]:
    """Save results to JSON files"""

    ensure_directories()
    output_dir = Path(__file__).parent.parent / "output"

    # Generate timestamps
    timestamp = datetime.now().strftime(config.output.timestamp_format)

    # Save definitions
    definitions_filename = config.output.definitions_filename_pattern.format(
        timestamp=timestamp
    )
    definitions_path = output_dir / definitions_filename

    definitions_data = {
        "processing_metadata": batch_result.processing_metadata.model_dump(),
        "results": [
            {
                "url": result.url,
                "reasoning_session_id": result.reasoning_session_id,
                "status": result.status.value,
                "processing_time_seconds": result.processing_time_seconds,
                "definitions": {
                    "attack_technique": (
                        result.attack_technique.model_dump()
                        if result.attack_technique
                        else None
                    ),
                    "detection_model": (
                        result.detection_model.model_dump()
                        if result.detection_model
                        else None
                    ),
                    "prevention_model": (
                        result.prevention_model.model_dump()
                        if result.prevention_model
                        else None
                    ),
                },
                "assessment": (
                    result.assessment.model_dump() if result.assessment else None
                ),
                "enrichment_sources": [
                    source.model_dump() for source in result.enrichment_sources
                ],
                "thought_history": result.thought_history,
                "errors": result.errors,
                "warnings": result.warnings,
            }
            for result in batch_result.results
        ],
    }

    with open(definitions_path, "w", encoding="utf-8") as f:
        json.dump(definitions_data, f, indent=2, default=str)

    # Save state log
    state_log_filename = config.output.state_log_filename_pattern.format(
        timestamp=timestamp
    )
    state_log_path = output_dir / state_log_filename

    state_log_data = [log.model_dump() for log in batch_result.state_logs]

    with open(state_log_path, "w", encoding="utf-8") as f:
        json.dump(state_log_data, f, indent=2, default=str)

    return definitions_path, state_log_path


@click.command()
@click.argument("urls", nargs=-1, required=True)
@click.option("--config", "-c", help="Path to configuration file")
@click.option("--verbose", "-v", is_flag=True, help="Enable verbose logging")
def cli(urls: tuple[str, ...], config: str, verbose: bool):
    """Security Content Analyzer - Analyze security articles to generate AT, DoM, and PoM definitions.

    URLS: One or more article URLs to analyze
    """

    async def main():
        try:
            # Load configuration
            app_config = load_config(config)

            # Override log level if verbose
            if verbose:
                app_config.logging.level = "DEBUG"

            # Validate configuration
            config_errors = validate_config(app_config)
            if config_errors:
                click.echo("Configuration errors:", err=True)
                for error in config_errors:
                    click.echo(f"  - {error}", err=True)
                sys.exit(1)

            # Setup logging
            await setup_logging(app_config)

            # Convert URLs tuple to list
            url_list = list(urls)

            logger.info(f"Security Content Analyzer starting with {len(url_list)} URLs")

            # Process articles
            batch_result = await process_articles_async(url_list, app_config)

            # Save results
            definitions_path, state_log_path = save_results(batch_result, app_config)

            # Print summary
            metadata = batch_result.processing_metadata
            click.echo(f"\n✅ Analysis complete!")
            click.echo(f"📊 Processed: {metadata.articles_processed} articles")
            click.echo(f"✅ Successful: {metadata.successful_articles}")
            click.echo(f"❌ Failed: {metadata.failed_articles}")
            click.echo(f"⏱️  Total time: {metadata.total_processing_time:.1f}s")
            click.echo(f"\n📁 Output files:")
            click.echo(f"   Definitions: {definitions_path}")
            click.echo(f"   State log: {state_log_path}")

            # Print definitions summary
            defs = metadata.definitions_generated
            if any(defs.values()):
                click.echo(f"\n📋 Generated definitions:")
                if defs["attack_techniques"]:
                    click.echo(f"   🎯 Attack Techniques: {defs['attack_techniques']}")
                if defs["detection_models"]:
                    click.echo(f"   🔍 Detection Models: {defs['detection_models']}")
                if defs["prevention_models"]:
                    click.echo(f"   🛡️  Prevention Models: {defs['prevention_models']}")

            # Print any errors
            all_errors = []
            for result in batch_result.results:
                all_errors.extend(result.errors)

            if all_errors:
                click.echo(f"\n⚠️  Errors encountered:")
                for error in all_errors[:5]:  # Show first 5 errors
                    click.echo(f"   - {error}")
                if len(all_errors) > 5:
                    click.echo(f"   ... and {len(all_errors) - 5} more (see log files)")

        except KeyboardInterrupt:
            logger.info("Analysis interrupted by user")
            click.echo("\n❌ Analysis interrupted by user")
            sys.exit(1)
        except Exception as e:
            logger.error(f"Fatal error: {e}")
            click.echo(f"\n❌ Fatal error: {e}", err=True)
            sys.exit(1)

    # Run the async main function
    asyncio.run(main())


if __name__ == "__main__":
    cli()
