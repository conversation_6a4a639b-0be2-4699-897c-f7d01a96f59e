{"processing_metadata": {"started_at": "2025-06-01 15:58:27.291592", "completed_at": "2025-06-01 15:58:29.112296", "articles_processed": 1, "successful_articles": 0, "failed_articles": 1, "definitions_generated": {"attack_techniques": 0, "detection_models": 0, "prevention_models": 0}, "total_processing_time": 1.820704}, "results": [{"url": "https://example.com/test-article", "reasoning_session_id": "9f44ae6c-7c8f-4664-b4f5-32f9510fea23", "status": "failed", "processing_time_seconds": null, "definitions": {"attack_technique": null, "detection_model": null, "prevention_model": null}, "assessment": null, "enrichment_sources": [], "errors": ["status_code: 401, model_name: gpt-4, body: {'message': 'Incorrect API key provided: sk-test-***********lder. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}"], "warnings": []}]}