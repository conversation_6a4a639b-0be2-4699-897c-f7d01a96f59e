"""Configuration management for Security Content Analyzer"""

import os
import toml
from pathlib import Path
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field
from dotenv import load_dotenv


class LLMConfig(BaseModel):
    """LLM configuration"""

    model: str = "gpt-4"
    temperature: float = Field(ge=0, le=2, default=0.2)
    max_tokens: int = Field(gt=0, default=4000)
    retry_attempts: int = Field(ge=1, default=3)


class LoggingConfig(BaseModel):
    """Logging configuration"""

    level: str = "INFO"
    format: str = "{time} | {level} | {message}"
    rotation: str = "10 MB"


class MCPConfig(BaseModel):
    """MCP server configuration"""

    sequential_thinking_path: str = "npx"
    sequential_thinking_args: list[str] = Field(
        default_factory=lambda: ["sequential-thinking-server"]
    )
    memgraph_path: str = "python"
    memgraph_args: list[str] = Field(default_factory=list)


class ProcessingConfig(BaseModel):
    """Processing configuration"""

    max_enrichment_rounds: int = Field(ge=0, default=3)
    enrichment_timeout: int = Field(gt=0, default=30)
    article_fetch_timeout: int = Field(gt=0, default=30)
    max_content_length: int = Field(gt=0, default=50000)


class OutputConfig(BaseModel):
    """Output configuration"""

    definitions_filename_pattern: str = "definitions_{timestamp}.json"
    state_log_filename_pattern: str = "state_log_{timestamp}.json"
    timestamp_format: str = "%Y%m%d_%H%M%S"


class ValidationConfig(BaseModel):
    """Validation configuration"""

    cypher_timeout: int = Field(gt=0, default=5000)
    max_validation_retries: int = Field(ge=0, default=3)


class SchemasConfig(BaseModel):
    """Schema configuration with example templates"""

    attack_technique_example: str = ""
    detection_model_example: str = ""
    prevention_model_example: str = ""


class Config(BaseModel):
    """Main configuration class"""

    llm: LLMConfig = Field(default_factory=LLMConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    mcp: MCPConfig = Field(default_factory=MCPConfig)
    processing: ProcessingConfig = Field(default_factory=ProcessingConfig)
    output: OutputConfig = Field(default_factory=OutputConfig)
    validation: ValidationConfig = Field(default_factory=ValidationConfig)
    schemas: SchemasConfig = Field(default_factory=SchemasConfig)

    # Environment variables
    openai_api_key: Optional[str] = None
    logfire_token: Optional[str] = None
    memgraph_uri: str = "bolt://localhost:7687"
    memgraph_username: Optional[str] = None
    memgraph_password: Optional[str] = None


def load_config(config_path: Optional[str] = None) -> Config:
    """Load configuration from TOML file and environment variables"""

    # Load environment variables
    load_dotenv()

    # Default config path
    if config_path is None:
        config_path = Path(__file__).parent.parent / "config" / "settings.toml"

    # Load TOML configuration
    config_data = {}
    if Path(config_path).exists():
        with open(config_path, "r") as f:
            config_data = toml.load(f)

    # Create config object
    config = Config(**config_data)

    # Override with environment variables
    config.openai_api_key = os.getenv("OPENAI_API_KEY")
    config.logfire_token = os.getenv("LOGFIRE_TOKEN")
    config.memgraph_uri = os.getenv("MEMGRAPH_URI", config.memgraph_uri)
    config.memgraph_username = os.getenv("MEMGRAPH_USERNAME")
    config.memgraph_password = os.getenv("MEMGRAPH_PASSWORD")

    # Update MCP args with environment variables
    if "${MEMGRAPH_URI}" in str(config.mcp.memgraph_args):
        config.mcp.memgraph_args = [
            arg.replace("${MEMGRAPH_URI}", config.memgraph_uri)
            for arg in config.mcp.memgraph_args
        ]

    # Set default memgraph args if empty
    if not config.mcp.memgraph_args:
        config.mcp.memgraph_args = ["memgraph-mcp-server", "--url", config.memgraph_uri]

    return config


def validate_config(config: Config) -> list[str]:
    """Validate configuration and return list of errors"""
    errors = []

    if not config.openai_api_key:
        errors.append("OPENAI_API_KEY environment variable is required")

    if not config.openai_api_key or not config.openai_api_key.startswith("sk-"):
        errors.append(
            "OPENAI_API_KEY must be a valid OpenAI API key starting with 'sk-'"
        )

    if config.llm.temperature < 0 or config.llm.temperature > 2:
        errors.append("LLM temperature must be between 0 and 2")

    if config.llm.max_tokens <= 0:
        errors.append("LLM max_tokens must be positive")

    return errors


def get_output_directory() -> Path:
    """Get the output directory path"""
    return Path(__file__).parent.parent / "output"


def get_logs_directory() -> Path:
    """Get the logs directory path"""
    return Path(__file__).parent.parent / "logs"


def ensure_directories() -> None:
    """Ensure output and logs directories exist"""
    get_output_directory().mkdir(exist_ok=True)
    get_logs_directory().mkdir(exist_ok=True)
