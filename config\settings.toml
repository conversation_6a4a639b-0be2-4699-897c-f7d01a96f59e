[llm]
model = "gpt-4.1-mini"
temperature = 0.2
max_tokens = 4000
retry_attempts = 3

[logging]
level = "INFO"
format = "{time} | {level} | {message}"
rotation = "10 MB"

[mcp]
sequential_thinking_path = "npx"
sequential_thinking_args = ["sequential-thinking-server"]
memgraph_path = "python"
memgraph_args = ["memgraph-mcp-server", "--url", "${MEMGRAPH_URI}"]

[processing]
max_enrichment_rounds = 3
enrichment_timeout = 30
article_fetch_timeout = 60
max_content_length = 50000

[output]
definitions_filename_pattern = "definitions_{timestamp}.json"
state_log_filename_pattern = "state_log_{timestamp}.json"
timestamp_format = "%Y%m%d_%H%M%S"

[validation]
cypher_timeout = 5000
max_validation_retries = 3
